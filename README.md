# DPM Python版本 - 动态规划矩阵运算

这是MATLAB DPM (Dynamic Programming using Matrix operations) 函数的Python实现版本，用于解决动态规划问题。

## 原始参考

**原始MATLAB版本:**
- Sundstrom, O. and Guzzella, L., "A Generic Dynamic Programming Matlab Function", In Proceedings of the 18th IEEE International Conference on Control Applications, pages 1625-1630, Saint Petersburg, Russia, 2009
- Copyright 2008- Institute for Dynamic Systems and Control, Department of Mechanical and Process Engineering, ETH Zurich
- Author: <PERSON><PERSON>

**Python版本翻译:** 2024

## 功能特性

- ✅ 完整的动态规划算法实现
- ✅ 支持一维和多维状态空间
- ✅ 支持连续和离散输入
- ✅ 向后动态规划求解最优策略
- ✅ 前向仿真生成最优轨迹
- ✅ 中文注释和文档
- ✅ 兼容MATLAB接口的函数调用方式
- ✅ 包含测试示例和可视化

## 安装要求

```bash
pip install numpy matplotlib
```

## 基本用法

### 1. 获取默认选项

```python
from dpm import dpm

# 获取默认选项结构
options = dpm()
print(f"默认选项: {options}")
```

### 2. 生成测试模型

```python
# 生成一个有2个状态、1个输入的测试模型
dpm('my_model', 2, 1)
# 这将创建 my_model.py 文件
```

### 3. 完整的动态规划求解

```python
from dpm import dpm, create_simple_problem

# 定义系统模型
def my_model(inp, par=None):
    """
    系统模型函数
    inp.X[i]: 状态
    inp.U[i]: 输入  
    inp.W[i]: 扰动
    inp.Ts: 时间步长
    """
    # 状态更新方程
    X = {1: inp.X[1] + inp.U[1] * inp.Ts}
    
    # 代价函数
    C = {1: inp.U[1]**2 * inp.Ts}
    
    # 不可行性标志 (0=可行, 1=不可行)
    I = np.zeros_like(inp.X[1])
    
    return X, C, I

# 创建问题设置
grd, prb = create_simple_problem(
    x0=[0.0],              # 初始状态
    xf_bounds=[(9.0, 11.0)], # 最终状态约束
    x_bounds=[(-5.0, 15.0)], # 状态边界
    u_bounds=[(-3.0, 3.0)],  # 输入边界
    nx_values=[31],          # 状态网格点数
    nu_values=[21],          # 输入网格点数
    N=20,                    # 时间步数
    Ts=0.5                   # 时间步长
)

# 设置选项
options = dpm()
options.Verbose = 'on'  # 显示进度

# 求解动态规划问题
out, dyn = dpm(my_model, None, grd, prb, options)

# 查看结果
print(f"最终状态: {out['X'][1][-1]}")
print(f"总代价: {out['total_cost']}")
```

## 数据结构

### Grid (网格结构)
```python
grd.X0[i]        # 初始状态
grd.XN[i]        # 最终状态约束 {'lo': 下界, 'hi': 上界}
grd.Nx[i]        # 状态网格点数量列表
grd.Xn[i]        # 状态网格边界 {'lo': [下界列表], 'hi': [上界列表]}
grd.Nu[i]        # 输入网格点数量列表  
grd.Un[i]        # 输入网格边界 {'lo': [下界列表], 'hi': [上界列表]}
```

### Problem (问题参数)
```python
prb.Ts           # 时间步长
prb.N            # 时间步数
prb.N0           # 起始时间索引 (默认1)
prb.W[i]         # 扰动向量 (可选)
```

### Options (选项)
```python
options.Verbose     = 'on'/'off'  # 显示进度信息
options.MyInf       = 1e4         # 不可行状态的大数值
options.Minimize    = 1           # 1=最小化, 0=最大化
options.SaveMap     = 'on'/'off'  # 保存代价函数映射
```

## 运行测试

```bash
# 运行完整测试套件
python test_dpm.py

# 或者直接运行DPM模块查看示例
python dpm.py
```

测试包括：
1. **测试1**: 简单积分器系统 - 从x=0到达x=10，最小化输入能量
2. **测试2**: 带阻尼振荡器系统 - 从(1,0)到达原点附近

## 示例结果

运行测试后，您将看到：
- 动态规划求解进度
- 最优轨迹的可视化图表
- 状态、输入和代价的时间历程
- 相平面轨迹（对于二维系统）

## 文件说明

- `dpm.py` - 主要的DPM库文件
- `test_dpm.py` - 测试脚本和示例
- `README.md` - 本说明文件
- `auto_test_model.py` - 自动生成的测试模型（运行后生成）

## 与MATLAB版本的差异

1. **语法**: 使用Python语法而非MATLAB
2. **索引**: Python使用0基索引，但为了兼容性，状态和输入使用1基索引的字典
3. **数据结构**: 使用Python的dataclass和字典而非MATLAB结构体
4. **可视化**: 使用matplotlib而非MATLAB绘图

## 许可证

This Source Code Form is subject to the terms of the Mozilla Public License, v. 2.0. If a copy of the MPL was not distributed with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

## 贡献

欢迎提交问题报告和改进建议！这个Python版本实现了原始MATLAB DPM的核心功能，适用于大多数动态规划问题。
